import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

// Work type options
const WORK_TYPES = [
  "Boiler installations",
  "Boiler service and repair",
  "Reactive maintenance",
  "General plumbing",
  "Commercial plumbing",
  "Bathroom installations",
  "Commercial heating",
  "Underfloor heating systems",
  "Oil Boiler systems",
  "LPG Systems",
  "Heating system controls and wiring"
] as const;

// How they heard about us options
const HEAR_ABOUT_US_OPTIONS = [
  "Google",
  "Facebook",
  "Instagram",
  "LinkedIn",
  "Word of mouth",
  "Referral",
  "Other"
] as const;

export const CreateSubcontractorDtoSchema = z.object({
  // Personal Information
  Name: z.string().min(1, "Name is required"), // Required field for Zoho CRM
  Last_Name: z.string().min(1, "Last name is required"),
  Email: z.string().email("Valid email is required"),
  mobile: z.string().min(1, "Mobile number is required"),

  // Address Information
  address_Line1: z.string().min(1, "Address line 1 is required"),
  address_Line2: z.string().optional(),
  postCode: z.string().min(1, "Post code is required"),

  // Personal Details
  date_of_birth: z.string().min(1, "Date of birth is required"),
  hear_About_Us: z.enum(HEAR_ABOUT_US_OPTIONS).optional(),

  // Professional Information
  gas_registered: z.boolean().default(false),
  years_Experience: z.number().min(0, "Years of experience must be 0 or greater"),
  has_Own_Van: z.boolean().default(false),
  has_Own_Tools: z.boolean().default(false),
  work_Type: z.enum(WORK_TYPES),
  central_London: z.boolean().default(false),
  driving_License: z.boolean().default(false),
  public_Liability_Insurance: z.boolean().default(false),

  // Availability and Preferences
  availableDays: z.array(z.string()).optional(),
  accepted_Rates: z.boolean().default(false),
  out_Of_Hours_Work: z.boolean().default(false),
  emergency_Callouts: z.boolean().default(false),
  preferred_WorkT_ype: z.array(z.string()), // Array format for multiselect
  additional_Qualifications: z.string().optional(),

  // Owner field (required by Zoho)
  Owner: z.object({
    id: z.string(),
    full_name: z.string(),
    name: z.string()
  }),

  // System fields
  Subcontractors_Owner: z.string().optional(),
  Lead_Source: z.string().default("Subcontractor Application"),
  Lead_Status: z.string().default("New Application"),
  Description: z.string().optional(),
});

export type CreateSubcontractorDto = z.infer<typeof CreateSubcontractorDtoSchema>;

// Input schema for the API endpoint (matches the form data structure)
export const SubcontractorInputSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Valid email is required"),
  mobile: z.string().min(1, "Mobile number is required"),
  addressLine1: z.string().min(1, "Address line 1 is required"),
  addressLine2: z.string().optional(),
  postCode: z.string().min(1, "Post code is required"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  hearAboutUs: z.string().optional(),
  contactConsent: z.boolean().default(false),
  yearsExperience: z.number().min(0, "Years of experience must be 0 or greater"),
  travelDistance: z.number().min(0, "Travel distance must be 0 or greater"),
  additionalQualifications: z.string().optional(),
  referred: z.string().optional(),
  referrerName: z.string().optional(),
  
  // Boolean fields that come as strings from form data
  gasRegistered: z.boolean().default(false),
  hasOwnVan: z.boolean().default(false),
  hasOwnTools: z.boolean().default(false),
  centralLondon: z.boolean().default(false),
  drivingLicense: z.boolean().default(false),
  publicLiabilityInsurance: z.boolean().default(false),
  acceptedRates: z.boolean().default(false),
  outOfHoursWork: z.boolean().default(false),
  emergencyCallouts: z.boolean().default(false),
  
  // Work type and preferences
  workType: z.string().optional(),
  preferredWorkType: z.union([z.string(), z.array(z.string())]).optional(), // Can be string or array
  availableDays: z.array(z.string()).optional(),
});

export type SubcontractorInput = z.infer<typeof SubcontractorInputSchema>;

export const SubcontractorSchema = CreateSubcontractorDtoSchema.merge(
  z.object({ 
    id: z.string(),
    Created_Time: z.string().optional(),
    Modified_Time: z.string().optional(),
  })
);

export type Subcontractor = z.infer<typeof SubcontractorSchema>;

// Response type for Zoho API
export type ZohoSubcontractorResponse = {
  success: boolean;
  data?: any;
  error?: string;
  recordId?: string;
  details?: any;
  zohoData?: any;
};
