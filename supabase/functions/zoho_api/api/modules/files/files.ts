import { <PERSON>oho<PERSON><PERSON> } from "../../zoho.ts";
import AccessTokenStore from "../../../store.ts";
import { env } from "../../../../_shared/env.ts";
import { HttpMethod } from "../../../types/common.ts";

export interface ZohoFileUploadResponse {
  success: boolean;
  data?: {
    file_id: string;
    file_name: string;
  }[];
  error?: string;
}

export class FilesService {
  private zohoAPI: ZohoApi;

  constructor(zohoAPI: ZohoApi) {
    this.zohoAPI = zohoAPI;
  }

  /**
   * Upload a file to Zoho CRM
   */
  public async uploadFile(file: File): Promise<ZohoFileUploadResponse> {
    try {
      const tokenStore = AccessTokenStore.getInstance();
      const token = await tokenStore.getAccessToken();

      // Convert file to base64
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const base64String = btoa(String.fromCharCode(...uint8Array));

      const endpoint = `${env.ZOHO_API_URL}/crm/v2/files`;
      const method: HttpMethod = "POST";
      
      // For file uploads, we need to use multipart/form-data
      const formData = new FormData();
      formData.append('file', file);

      const headers = {
        Authorization: `Zoho-oauthtoken ${token.trim()}`,
        // Don't set Content-Type for FormData, let the browser set it with boundary
      };

      const response = await fetch(`${env.ZOHO_API_URL}/crm/v2/files`, {
        method: 'POST',
        headers: headers,
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Zoho file upload error:", errorText);
        throw new Error(`File upload failed: ${errorText}`);
      }

      const result = await response.json();
      console.log("File upload result:", result);

      return {
        success: true,
        data: result.data
      };

    } catch (error) {
      console.error("Error uploading file to Zoho:", error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to upload file to Zoho CRM"
      };
    }
  }

  /**
   * Upload multiple files to Zoho CRM
   */
  public async uploadFiles(files: File[]): Promise<ZohoFileUploadResponse> {
    try {
      const uploadPromises = files.map(file => this.uploadFile(file));
      const results = await Promise.all(uploadPromises);
      
      // Check if any uploads failed
      const failedUploads = results.filter(result => !result.success);
      if (failedUploads.length > 0) {
        return {
          success: false,
          error: `${failedUploads.length} file(s) failed to upload`
        };
      }

      // Combine all successful uploads
      const allFileData = results.flatMap(result => result.data || []);
      
      return {
        success: true,
        data: allFileData
      };

    } catch (error) {
      console.error("Error uploading files to Zoho:", error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to upload files to Zoho CRM"
      };
    }
  }

  /**
   * Format file data for Zoho record field
   * Converts uploaded file data to the format expected by Zoho file fields
   */
  public formatFilesForRecord(fileData: { file_id: string; file_name: string }[]): any[] {
    return fileData.map(file => ({
      file_id: file.file_id,
      file_name: file.file_name
    }));
  }
}
