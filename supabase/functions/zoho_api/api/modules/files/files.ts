

export interface ZohoFileUploadResponse {
  success: boolean;
  data?: {
    file_id: string;
    file_name: string;
  }[];
  error?: string;
}

export class FilesService {
  constructor() {
    // No dependencies needed for base64 conversion
  }

  /**
   * Generate a file ID hash from file content
   */
  private async generateFileId(file: File): Promise<string> {
    // Create a hash from file name, size, and timestamp
    const fileInfo = `${file.name}_${file.size}_${Date.now()}`;
    const encoder = new TextEncoder();
    const data = encoder.encode(fileInfo);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    return hashHex;
  }

  /**
   * Convert file to base64 format and generate file ID
   */
  public async processFile(file: File): Promise<{ success: boolean; data?: { file_id: string; file_name: string; content: string }; error?: string }> {
    try {
      // Convert file to base64
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const base64String = btoa(String.fromCharCode(...uint8Array));

      // Generate a file ID
      const fileId = await this.generateFileId(file);

      return {
        success: true,
        data: {
          file_id: fileId,
          file_name: file.name,
          content: base64String
        }
      };

    } catch (error) {
      console.error("Error processing file:", error);

      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to process file"
      };
    }
  }

  /**
   * Process files for direct inclusion in Zoho record
   */
  public async processFilesForRecord(files: File[]): Promise<ZohoFileUploadResponse> {
    try {
      const processedFiles: { file_id: string; file_name: string }[] = [];

      for (const file of files) {
        const fileResult = await this.processFile(file);

        if (!fileResult.success) {
          return {
            success: false,
            error: `Failed to process file ${file.name}: ${fileResult.error}`
          };
        }

        processedFiles.push({
          file_id: fileResult.data!.file_id,
          file_name: fileResult.data!.file_name
        });
      }

      return {
        success: true,
        data: processedFiles
      };

    } catch (error) {
      console.error("Error processing files:", error);

      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to process files"
      };
    }
  }

  /**
   * Process multiple files for Zoho CRM (using base64 encoding)
   */
  public async uploadFiles(files: File[]): Promise<ZohoFileUploadResponse> {
    return await this.processFilesForRecord(files);
  }

  /**
   * Format file data for Zoho record field
   * Converts uploaded file data to the format expected by Zoho file fields
   */
  public formatFilesForRecord(fileData: { file_id: string; file_name: string }[]): any[] {
    return fileData.map(file => ({
      file_id: file.file_id,
      file_name: file.file_name
    }));
  }
}
