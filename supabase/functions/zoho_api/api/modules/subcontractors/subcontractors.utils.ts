import { SubcontractorInput, CreateSubcontractorDto } from "../../../types/subcontractor.ts";

export class SubcontractorsServiceUtils {
  
  /**
   * Transform input data from the API request to Zoho CRM format
   */
  public transformToZohoFormat(input: SubcontractorInput): CreateSubcontractorDto {
    return {
      // Personal Information
      Name: `${input.firstName} ${input.lastName}`, // Required field for Zoho CRM
      Last_Name: input.lastName,
      Email: input.email,
      Mobile: input.mobile,

      // Address Information
      address_Line1: input.addressLine1,
      Address_Line2: input.addressLine2,
      PostCode: input.postCode,

      // Personal Details
      date_of_birth: this.formatDateForZoho(input.dateOfBirth),
      hear_About_Us: this.mapHearAboutUs(input.hearAboutUs),

      // Professional Information
      gas_registered: input.gasRegistered || false,
      years_Experience: input.yearsExperience || 0,
      has_Own_Van: input.hasOwnVan || false,
      has_Own_Tools: input.hasOwnTools || false,
      work_Type: this.mapWorkType(input.workType),
      central_London: input.centralLondon || false,
      driving_License: input.drivingLicense || false,
      public_Liability_Insurance: input.publicLiabilityInsurance || false,

      // Availability and Preferences
      availableDays: this.mapAvailableDays(input.availableDays),
      accepted_Rates: input.acceptedRates || false,
      out_Of_Hours_Work: input.outOfHoursWork || false,
      emergency_Callouts: input.emergencyCallouts || false,
      preferredWorkType: this.mapPreferredWorkType(input.preferredWorkType),
      additional_Qualifications: input.additionalQualifications,

      // Owner field (required by Zoho)
      Owner: {
        id: "672785000002880001", // Default owner ID from your example
        full_name: "dev dev",
        name: "dev dev"
      },

      // System fields
      Lead_Source: "Subcontractor Application",
      Lead_Status: "New Application",
      Description: this.generateDescription(input),
    };
  }

  /**
   * Format date for Zoho CRM (YYYY-MM-DD format)
   */
  public formatDateForZoho(dateString: string): string {
    try {
      // Handle different date formats (MM/DD/YYYY, DD/MM/YYYY, etc.)
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        // Try parsing MM/DD/YYYY format
        const parts = dateString.split('/');
        if (parts.length === 3) {
          const [month, day, year] = parts;
          const parsedDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
          if (!isNaN(parsedDate.getTime())) {
            return parsedDate.toISOString().split('T')[0];
          }
        }
        return dateString; // Return original if can't parse
      }
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.warn("Error formatting date:", error);
      return dateString;
    }
  }

  /**
   * Map "Hear About Us" field to valid Zoho options
   */
  private mapHearAboutUs(hearAboutUs?: string): "Google" | "Facebook" | "Instagram" | "LinkedIn" | "Word of mouth" | "Referral" | "Other" | undefined {
    if (!hearAboutUs) return undefined;
    
    const lowerCase = hearAboutUs.toLowerCase();
    
    if (lowerCase.includes('google')) return "Google";
    if (lowerCase.includes('facebook')) return "Facebook";
    if (lowerCase.includes('instagram')) return "Instagram";
    if (lowerCase.includes('linkedin')) return "LinkedIn";
    if (lowerCase.includes('word of mouth') || lowerCase.includes('word-of-mouth')) return "Word of mouth";
    if (lowerCase.includes('referral') || lowerCase.includes('refer')) return "Referral";
    
    return "Other";
  }

  /**
   * Map work type to valid Zoho options
   */
  private mapWorkType(workType?: string): "Boiler installations" | "Boiler service and repair" | "Reactive maintenance" | "General plumbing" | "Commercial plumbing" | "Bathroom installations" | "Commercial heating" | "Underfloor heating systems" | "Oil Boiler systems" | "LPG Systems" | "Heating system controls and wiring" {
    if (!workType) return "General plumbing"; // Default
    
    const lowerCase = workType.toLowerCase();
    
    if (lowerCase.includes('boiler install')) return "Boiler installations";
    if (lowerCase.includes('boiler service') || lowerCase.includes('boiler repair')) return "Boiler service and repair";
    if (lowerCase.includes('reactive') || lowerCase.includes('maintenance')) return "Reactive maintenance";
    if (lowerCase.includes('commercial plumbing')) return "Commercial plumbing";
    if (lowerCase.includes('bathroom')) return "Bathroom installations";
    if (lowerCase.includes('commercial heating')) return "Commercial heating";
    if (lowerCase.includes('underfloor')) return "Underfloor heating systems";
    if (lowerCase.includes('oil boiler')) return "Oil Boiler systems";
    if (lowerCase.includes('lpg')) return "LPG Systems";
    if (lowerCase.includes('controls') || lowerCase.includes('wiring')) return "Heating system controls and wiring";
    
    return "General plumbing"; // Default
  }

  /**
   * Map preferred work type to valid Zoho multiselect format
   * Zoho expects array format for multiselect: ["Reactive", "Maintenance", "Installations"]
   */
  private mapPreferredWorkType(preferredWorkType?: string | string[]): string[] {
    if (!preferredWorkType) return ["Installations"]; // Default

    // Handle both string and array inputs
    let workTypes: string[] = [];

    if (Array.isArray(preferredWorkType)) {
      workTypes = preferredWorkType;
    } else {
      workTypes = [preferredWorkType];
    }

    // Valid Zoho options based on the field definition
    const validOptions = ["Reactive", "Maintenance", "Installations"];
    const mappedTypes: string[] = [];

    workTypes.forEach(type => {
      const lowerCase = type.toLowerCase();

      // Map frontend values to Zoho values
      if (lowerCase.includes('maintenance')) {
        mappedTypes.push("Maintenance");
      } else if (lowerCase.includes('reactive')) {
        mappedTypes.push("Reactive");
      } else if (lowerCase.includes('installations')) {
        mappedTypes.push("Installations");
      }
    });

    // Remove duplicates and ensure we have valid options
    const uniqueTypes = [...new Set(mappedTypes)].filter(type => validOptions.includes(type));

    // Return array as expected by Zoho multiselect
    return uniqueTypes.length > 0 ? uniqueTypes : ["Installations"];
  }

  /**
   * Map available days to Zoho format (3-letter day names)
   * Zoho expects: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
   */
  private mapAvailableDays(availableDays?: string[]): string[] {
    if (!availableDays || availableDays.length === 0) return [];

    const dayMapping: { [key: string]: string } = {
      'Monday': 'Mon',
      'Tuesday': 'Tue',
      'Wednesday': 'Wed',
      'Thursday': 'Thu',
      'Friday': 'Fri',
      'Saturday': 'Sat',
      'Sunday': 'Sun',
      // Also handle short forms in case they're already provided
      'Mon': 'Mon',
      'Tue': 'Tue',
      'Wed': 'Wed',
      'Thu': 'Thu',
      'Fri': 'Fri',
      'Sat': 'Sat',
      'Sun': 'Sun'
    };

    return availableDays
      .map(day => dayMapping[day])
      .filter(day => day !== undefined);
  }



  /**
   * Generate description field for Zoho CRM
   */
  private generateDescription(input: SubcontractorInput): string {
    return `
Subcontractor Application Details:
================================

Personal Information:
- Name: ${input.firstName} ${input.lastName}
- Email: ${input.email}
- Mobile: ${input.mobile}
- Address: ${input.addressLine1}${input.addressLine2 ? ', ' + input.addressLine2 : ''}
- Post Code: ${input.postCode}
- Date of Birth: ${input.dateOfBirth}
- How they heard about us: ${input.hearAboutUs || 'Not specified'}

Professional Details:
- Gas Registered: ${input.gasRegistered ? 'Yes' : 'No'}
- Years Experience: ${input.yearsExperience || 0}
- Travel Distance: ${input.travelDistance || 0} miles
- Has Own Van: ${input.hasOwnVan ? 'Yes' : 'No'}
- Has Own Tools: ${input.hasOwnTools ? 'Yes' : 'No'}
- Work Type: ${input.workType || 'Not specified'}
- Preferred Work Type: ${input.preferredWorkType || 'Not specified'}

Location & Availability:
- Central London: ${input.centralLondon ? 'Yes' : 'No'}
- Available Days: ${input.availableDays?.join(", ") || "Not specified"}
- Out Of Hours Work: ${input.outOfHoursWork ? 'Yes' : 'No'}
- Emergency Callouts: ${input.emergencyCallouts ? 'Yes' : 'No'}

Compliance:
- Driving License: ${input.drivingLicense ? 'Yes' : 'No'}
- Public Liability Insurance: ${input.publicLiabilityInsurance ? 'Yes' : 'No'}
- Accepted Rates: ${input.acceptedRates ? 'Yes' : 'No'}

Additional Information:
- Additional Qualifications: ${input.additionalQualifications || "None specified"}
- Contact Consent: ${input.contactConsent ? 'Yes' : 'No'}
- Referred: ${input.referred || 'No'}
- Referrer Name: ${input.referrerName || 'N/A'}

Application submitted on: ${new Date().toISOString()}
    `.trim();
  }
}
